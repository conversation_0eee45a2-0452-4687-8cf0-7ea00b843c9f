openapi: 3.0.3
info:
  title: Teaching Assistant API - 清空教室周课表功能更新
  description: 新增清空教室周课表功能的API接口文档
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  /principal/courses/classroom-week:
    delete:
      tags:
        - 课程管理 - 校长端
      summary: 清空教室周课表
      description: |
        清空指定教室在指定周次（日期范围）内的所有课程安排。
        
        **功能特点：**
        - 只删除指定教室的课程，不影响其他教室
        - 只删除指定日期范围内的课程，不影响其他时间的课程
        - 支持权限验证，只有校长可以操作本校的课程
        - 返回删除的课程数量
        
        **使用场景：**
        - 教室维修需要清空整周课程
        - 重新安排教室课程表
        - 批量清理过期或错误的课程安排
      operationId: clearClassroomWeekSchedule
      security:
        - bearerAuth: []
      parameters:
        - name: classroomId
          in: query
          required: true
          description: 教室ID
          schema:
            type: integer
            format: int64
            example: 1
        - name: startDate
          in: query
          required: true
          description: 开始日期（包含），格式：YYYY-MM-DD
          schema:
            type: string
            format: date
            example: "2024-01-15"
        - name: endDate
          in: query
          required: true
          description: 结束日期（包含），格式：YYYY-MM-DD
          schema:
            type: string
            format: date
            example: "2024-01-21"
      responses:
        '200':
          description: 清空成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                success:
                  summary: 成功清空课表
                  value:
                    code: 200
                    message: "清空教室周课表成功，共删除5个课程"
                    data: null
                    timestamp: "2024-01-15T10:30:00Z"
                no_courses:
                  summary: 没有课程需要删除
                  value:
                    code: 200
                    message: "清空教室周课表成功，共删除0个课程"
                    data: null
                    timestamp: "2024-01-15T10:30:00Z"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                missing_params:
                  summary: 缺少必需参数
                  value:
                    code: 400
                    message: "教室ID不能为空"
                    data: null
                    timestamp: "2024-01-15T10:30:00Z"
                invalid_date_range:
                  summary: 日期范围无效
                  value:
                    code: 400
                    message: "开始日期不能晚于结束日期"
                    data: null
                    timestamp: "2024-01-15T10:30:00Z"
                invalid_date_format:
                  summary: 日期格式错误
                  value:
                    code: 400
                    message: "日期格式错误"
                    data: null
                    timestamp: "2024-01-15T10:30:00Z"
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 401
                message: "未授权访问"
                data: null
                timestamp: "2024-01-15T10:30:00Z"
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 403
                message: "无权操作该教室的课程"
                data: null
                timestamp: "2024-01-15T10:30:00Z"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 500
                message: "系统错误"
                data: null
                timestamp: "2024-01-15T10:30:00Z"

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          nullable: true
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
          example: "2024-01-15T10:30:00Z"
      required:
        - code
        - message
        - timestamp

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        使用JWT Bearer Token进行身份验证。
        
        **获取Token：**
        1. 调用登录接口获取访问令牌
        2. 在请求头中添加：Authorization: Bearer {token}
        
        **权限要求：**
        - 必须是校长角色
        - 只能操作本校的教室和课程

tags:
  - name: 课程管理 - 校长端
    description: 校长端课程管理相关接口

# 更新说明
x-changelog:
  version: "1.0.0"
  date: "2024-01-15"
  changes:
    - type: "新增"
      description: "新增清空教室周课表接口"
      endpoint: "DELETE /principal/courses/classroom-week"
      details:
        - "支持按教室和日期范围批量删除课程"
        - "包含完整的权限验证机制"
        - "返回删除的课程数量统计"
        - "支持前端确认对话框显示教室名称和周次信息"

# 使用示例
x-examples:
  frontend_usage:
    description: "前端调用示例"
    code: |
      // 清空教室周课表
      const clearWeekSchedule = async () => {
        try {
          await principalCourseApi.clearClassroomWeekSchedule({
            classroomId: 1,
            startDate: '2024-01-15',
            endDate: '2024-01-21'
          });
          ElMessage.success('清空本周课表成功');
        } catch (error) {
          ElMessage.error('清空课表失败');
        }
      };
  
  curl_example:
    description: "cURL调用示例"
    code: |
      curl -X DELETE "http://localhost:8080/api/principal/courses/classroom-week?classroomId=1&startDate=2024-01-15&endDate=2024-01-21" \
        -H "Authorization: Bearer your_jwt_token_here" \
        -H "Content-Type: application/json"
