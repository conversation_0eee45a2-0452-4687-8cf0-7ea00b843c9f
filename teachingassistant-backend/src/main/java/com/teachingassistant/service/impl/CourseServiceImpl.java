package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Course;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.CourseMapper;
import com.teachingassistant.mapper.UserMapper;
import com.teachingassistant.service.CourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseServiceImpl implements CourseService {

    private final CourseMapper courseMapper;
    private final UserMapper userMapper;
    
    @Override
    public Course findById(Long courseId) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        Course course = courseMapper.findById(courseId);
        if (course == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "课程不存在");
        }
        return course;
    }
    
    @Override
    public List<Course> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return courseMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<Course> findByDateRange(Long schoolId, LocalDate startDate, LocalDate endDate, 
                                       Long teacherId, Long classroomId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "日期范围不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始日期不能晚于结束日期");
        }
        
        return courseMapper.findByDateRange(schoolId, startDate, endDate, teacherId, classroomId);
    }
    
    @Override
    public PageResult<Course> findWithPagination(Integer page, Integer size, Long schoolId, 
                                                Long teacherId, Long classroomId, String status) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        
        Integer offset = (page - 1) * size;
        List<Course> courses = courseMapper.findWithPagination(offset, size, schoolId, 
                                                              teacherId, classroomId, status);
        Integer total = courseMapper.countCourses(schoolId, teacherId, classroomId, status);
        
        return PageResult.of(page, size, total.longValue(), courses);
    }
    
    @Override
    @Transactional
    public Course createCourse(Course course) {
        validateCourseForCreate(course);
        
        // 检查时间冲突
        if (hasTimeConflict(course.getTeacherId(), course.getClassroomId(),
                           course.getCourseDate(), course.getStartTime(),
                           course.getEndTime(), null)) {
            throw new BusinessException(ResultCode.SCHEDULE_CONFLICT, "该时间段已有课程安排");
        }
        
        // 设置默认状态
        if (!StringUtils.hasText(course.getStatus())) {
            course.setStatus("scheduled");
        }
        
        int result = courseMapper.insert(course);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程创建失败");
        }
        
        log.info("课程创建成功: 教师ID={}, 教室ID={}, 日期={}", 
                course.getTeacherId(), course.getClassroomId(), course.getCourseDate());
        return findById(course.getCourseId());
    }
    
    @Override
    @Transactional
    public Course updateCourse(Course course) {
        validateCourseForUpdate(course);
        
        // 检查课程是否存在
        Course existingCourse = findById(course.getCourseId());
        
        // 检查时间冲突（排除当前课程）
        if (hasTimeConflict(course.getTeacherId(), course.getClassroomId(),
                           course.getCourseDate(), course.getStartTime(),
                           course.getEndTime(), course.getCourseId())) {
            throw new BusinessException(ResultCode.SCHEDULE_CONFLICT, "该时间段已有课程安排");
        }
        
        int result = courseMapper.update(course);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程更新失败");
        }
        
        log.info("课程更新成功: ID={}", course.getCourseId());
        return findById(course.getCourseId());
    }
    
    @Override
    @Transactional
    public void deleteCourse(Long courseId) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        
        // 检查课程是否存在
        Course course = findById(courseId);
        
        int result = courseMapper.deleteById(courseId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "课程删除失败");
        }
        
        log.info("课程删除成功: ID={}", courseId);
    }
    
    @Override
    @Transactional
    public void deleteCourses(List<Long> courseIds) {
        if (courseIds == null || courseIds.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID列表不能为空");
        }

        int result = courseMapper.deleteByIds(courseIds);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "批量删除课程失败");
        }

        log.info("批量删除课程成功: 数量={}", result);
    }

    @Override
    @Transactional
    public int clearClassroomWeekSchedule(Long schoolId, Long classroomId, String startDate, String endDate) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (classroomId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        if (startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "日期范围不能为空");
        }

        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            if (start.isAfter(end)) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "开始日期不能晚于结束日期");
            }

            // 查询指定教室和日期范围内的所有课程
            List<Course> courses = courseMapper.findByDateRange(schoolId, start, end, null, classroomId);

            if (courses.isEmpty()) {
                log.info("教室{}在{}到{}期间没有课程安排", classroomId, startDate, endDate);
                return 0;
            }

            // 提取课程ID列表
            List<Long> courseIds = courses.stream()
                    .map(Course::getCourseId)
                    .collect(Collectors.toList());

            // 批量删除课程
            int result = courseMapper.deleteByIds(courseIds);

            log.info("清空教室{}周课表成功: 删除课程数量={}, 日期范围={} 到 {}",
                    classroomId, result, startDate, endDate);

            return result;
        } catch (DateTimeParseException e) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "日期格式错误");
        }
    }
    
    @Override
    @Transactional
    public void updateCourseStatus(Long courseId, String status) {
        if (courseId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
        if (!StringUtils.hasText(status)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "状态不能为空");
        }
        
        // 检查课程是否存在
        Course course = findById(courseId);
        
        int result = courseMapper.updateStatus(courseId, status);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "更新课程状态失败");
        }
        
        log.info("课程状态更新成功: ID={}, 状态={}", courseId, status);
    }
    
    @Override
    public boolean hasTimeConflict(Long teacherId, Long classroomId, LocalDate courseDate, 
                                  LocalTime startTime, LocalTime endTime, Long excludeCourseId) {
        if (teacherId == null || classroomId == null || courseDate == null || 
            startTime == null || endTime == null) {
            return false;
        }
        
        List<Course> conflictCourses = courseMapper.findConflictCourses(
            teacherId, classroomId, courseDate, startTime, endTime, excludeCourseId);
        
        return !conflictCourses.isEmpty();
    }
    
    @Override
    public Map<LocalDate, List<Map<String, Object>>> getAvailableTimeSlots(Long schoolId, Long teacherId, 
                                                                          Long classroomId, LocalDate startDate, 
                                                                          LocalDate endDate, String scheduleMode) {
        if (schoolId == null || teacherId == null || classroomId == null || 
            startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "参数不能为空");
        }
        
        Map<LocalDate, List<Map<String, Object>>> availableSlots = new HashMap<>();
        
        // 根据排课模式定义工作时间段
        List<Map<String, LocalTime>> workingHours;
        if ("extended".equals(scheduleMode)) {
            // 24小时模式：全天排课，结束时间设为23:30避免边界计算问题
            workingHours = Arrays.asList(
                Map.of("start", LocalTime.of(0, 0), "end", LocalTime.of(23, 30))
            );
        } else {
            // 标准模式：传统工作时间
            workingHours = Arrays.asList(
                Map.of("start", LocalTime.of(8, 0), "end", LocalTime.of(12, 0)),
                Map.of("start", LocalTime.of(14, 0), "end", LocalTime.of(18, 0))
            );
        }
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<Map<String, Object>> daySlots = new ArrayList<>();
            
            // 获取当天已有课程
            List<Course> teacherCourses = courseMapper.findByTeacherAndDate(teacherId, currentDate);
            List<Course> classroomCourses = courseMapper.findByClassroomAndDate(classroomId, currentDate);
            
            // 合并已占用时间段
            Set<Map<String, LocalTime>> occupiedSlots = new HashSet<>();
            teacherCourses.forEach(course -> 
                occupiedSlots.add(Map.of("start", course.getStartTime(), "end", course.getEndTime())));
            classroomCourses.forEach(course -> 
                occupiedSlots.add(Map.of("start", course.getStartTime(), "end", course.getEndTime())));
            
            // 计算空闲时段并智能合并
            daySlots = generateMergedAvailableSlots(workingHours, occupiedSlots);
            
            availableSlots.put(currentDate, daySlots);
            currentDate = currentDate.plusDays(1);
        }
        
        return availableSlots;
    }
    
    @Override
    public boolean hasPermissionToAccess(Long courseId, Long userSchoolId, String userRole) {
        if (courseId == null || userSchoolId == null || !StringUtils.hasText(userRole)) {
            return false;
        }
        
        Course course = courseMapper.findById(courseId);
        if (course == null) {
            return false;
        }
        
        // 超级管理员有所有权限
        if ("super_admin".equals(userRole)) {
            return true;
        }
        
        // 校长和老师只能访问本校的课程
        return course.getSchoolId().equals(userSchoolId);
    }
    
    @Override
    public Map<String, Object> getCourseStatistics(Long schoolId, LocalDate startDate, LocalDate endDate) {
        if (schoolId == null || startDate == null || endDate == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "参数不能为空");
        }
        
        List<Course> courses = courseMapper.findByDateRange(schoolId, startDate, endDate, null, null);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCourses", courses.size());
        statistics.put("scheduledCourses", courses.stream().mapToInt(c -> "scheduled".equals(c.getStatus()) ? 1 : 0).sum());
        statistics.put("completedCourses", courses.stream().mapToInt(c -> "completed".equals(c.getStatus()) ? 1 : 0).sum());
        statistics.put("cancelledCourses", courses.stream().mapToInt(c -> "cancelled".equals(c.getStatus()) ? 1 : 0).sum());
        
        return statistics;
    }
    
    private void validateCourseForCreate(Course course) {
        if (course == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程信息不能为空");
        }
        if (course.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (course.getTeacherId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教师ID不能为空");
        }
        if (course.getStudentId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学生ID不能为空");
        }
        if (course.getClassroomId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        if (course.getCourseDate() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "上课日期不能为空");
        }
        if (course.getStartTime() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始时间不能为空");
        }
        if (course.getEndTime() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "结束时间不能为空");
        }
        if (course.getStartTime().isAfter(course.getEndTime()) || 
            course.getStartTime().equals(course.getEndTime())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "开始时间必须早于结束时间");
        }
        if (course.getPrice() == null || course.getPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程价格不能为空且不能为负数");
        }
    }
    
    private void validateCourseForUpdate(Course course) {
        validateCourseForCreate(course);
        if (course.getCourseId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "课程ID不能为空");
        }
    }

    @Override
    public List<User> findAvailableTeachers(Long schoolId, LocalDate courseDate,
                                          LocalTime startTime, LocalTime endTime, String subject) {
        if (schoolId == null || courseDate == null || startTime == null || endTime == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "参数不能为空");
        }

        return userMapper.findAvailableTeachers(schoolId, courseDate, startTime, endTime, subject);
    }

    /**
     * 生成智能合并的空闲时段
     *
     * 缓冲时间规则：
     * - 空闲时段结束时间：下一个课程开始时间减1分钟
     * - 空闲时段开始时间：上一个课程结束时间加1分钟
     *
     * 示例：
     * - 课程A: 10:00-12:06，课程B: 15:13-17:00
     * - 空闲时段: 12:07-15:12（12:06+1分钟 到 15:13-1分钟）
     *
     * @param workingHours 工作时间段
     * @param occupiedSlots 已占用时间段
     * @return 合并后的空闲时段列表
     */
    private List<Map<String, Object>> generateMergedAvailableSlots(
            List<Map<String, LocalTime>> workingHours,
            Set<Map<String, LocalTime>> occupiedSlots) {

        List<Map<String, Object>> mergedSlots = new ArrayList<>();

        for (Map<String, LocalTime> workingHour : workingHours) {
            LocalTime workStart = workingHour.get("start");
            LocalTime workEnd = workingHour.get("end");

            // 获取当前工作时间段内的所有占用时间段，并按开始时间排序
            List<Map<String, LocalTime>> sortedOccupiedSlots = occupiedSlots.stream()
                .filter(occupied -> {
                    LocalTime occStart = occupied.get("start");
                    LocalTime occEnd = occupied.get("end");
                    // 检查占用时间段是否与工作时间段有重叠
                    return !(occEnd.isBefore(workStart) || occStart.isAfter(workEnd) || occStart.equals(workEnd));
                })
                .sorted((a, b) -> a.get("start").compareTo(b.get("start")))
                .collect(java.util.stream.Collectors.toList());

            // 计算空闲时段（考虑1分钟缓冲时间）
            LocalTime currentTime = workStart;

            for (Map<String, LocalTime> occupied : sortedOccupiedSlots) {
                LocalTime occStart = occupied.get("start");
                LocalTime occEnd = occupied.get("end");

                // 计算空闲时段结束时间：课程开始前1分钟
                LocalTime freeEndTime = occStart.minusMinutes(1);

                // 如果当前时间在占用时间段开始之前，且有足够的空闲时间（至少2分钟），则有空闲时段
                if (currentTime.isBefore(freeEndTime) && !currentTime.equals(freeEndTime)) {
                    LocalTime actualFreeEnd = freeEndTime.isBefore(workEnd) ? freeEndTime : workEnd;
                    if (currentTime.isBefore(actualFreeEnd)) {
                        Map<String, Object> slot = new HashMap<>();
                        slot.put("startTime", currentTime);
                        slot.put("endTime", actualFreeEnd);
                        slot.put("available", true);
                        slot.put("merged", true); // 标记为合并的时段
                        mergedSlots.add(slot);
                    }
                }

                // 更新当前时间为占用时间段结束后1分钟
                LocalTime nextAvailableTime = occEnd.plusMinutes(1);
                currentTime = nextAvailableTime.isAfter(currentTime) ? nextAvailableTime : currentTime;
            }

            // 检查最后一个占用时间段之后是否还有空闲时间
            if (currentTime.isBefore(workEnd)) {
                Map<String, Object> slot = new HashMap<>();
                slot.put("startTime", currentTime);
                slot.put("endTime", workEnd);
                slot.put("available", true);
                slot.put("merged", true); // 标记为合并的时段
                mergedSlots.add(slot);
            }
        }

        return mergedSlots;
    }
}
