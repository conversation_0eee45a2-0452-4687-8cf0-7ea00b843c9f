package com.teachingassistant.service;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.entity.Course;
import com.teachingassistant.entity.User;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 课程服务接口
 *
 * <AUTHOR> Assistant System
 */
public interface CourseService {

    /**
     * 根据课程ID查询课程
     */
    Course findById(Long courseId);

    /**
     * 根据学校ID查询课程列表
     */
    List<Course> findBySchoolId(Long schoolId);

    /**
     * 查询指定日期范围内的课程
     */
    List<Course> findByDateRange(Long schoolId, LocalDate startDate, LocalDate endDate, 
                                Long teacherId, Long classroomId);

    /**
     * 分页查询课程列表
     */
    PageResult<Course> findWithPagination(Integer page, Integer size, Long schoolId, 
                                         Long teacherId, Long classroomId, String status);

    /**
     * 创建课程
     */
    Course createCourse(Course course);

    /**
     * 更新课程信息
     */
    Course updateCourse(Course course);

    /**
     * 删除课程
     */
    void deleteCourse(Long courseId);

    /**
     * 批量删除课程
     */
    void deleteCourses(List<Long> courseIds);

    /**
     * 更新课程状态
     */
    void updateCourseStatus(Long courseId, String status);

    /**
     * 检查时间冲突
     */
    boolean hasTimeConflict(Long teacherId, Long classroomId, LocalDate courseDate, 
                           LocalTime startTime, LocalTime endTime, Long excludeCourseId);

    /**
     * 获取空闲时段
     * @param schoolId 学校ID
     * @param teacherId 教师ID
     * @param classroomId 教室ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param scheduleMode 排课模式：standard(标准模式8:00-12:00,14:00-18:00) | extended(24小时模式)
     * @return 空闲时段列表，按日期分组
     */
    Map<LocalDate, List<Map<String, Object>>> getAvailableTimeSlots(Long schoolId, Long teacherId, 
                                                                   Long classroomId, LocalDate startDate, 
                                                                   LocalDate endDate, String scheduleMode);

    /**
     * 验证用户是否有权限访问指定课程
     */
    boolean hasPermissionToAccess(Long courseId, Long userSchoolId, String userRole);

    /**
     * 获取课程统计信息
     */
    Map<String, Object> getCourseStatistics(Long schoolId, LocalDate startDate, LocalDate endDate);

    /**
     * 根据时间段和学科查询空闲教师列表
     * @param schoolId 学校ID
     * @param courseDate 课程日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param subject 学科（可选）
     * @return 空闲教师列表
     */
    List<User> findAvailableTeachers(Long schoolId, LocalDate courseDate,
                                   LocalTime startTime, LocalTime endTime, String subject);
}
