package com.teachingassistant.mapper;

import com.teachingassistant.entity.Classroom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 教室数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface ClassroomMapper {
    
    /**
     * 根据教室ID查询教室
     */
    Classroom findById(@Param("classroomId") Long classroomId);
    
    /**
     * 根据学校ID查询教室列表
     */
    List<Classroom> findBySchoolId(@Param("schoolId") Long schoolId);
    
    /**
     * 查询所有教室列表
     */
    List<Classroom> findAll();
    
    /**
     * 分页查询教室列表（管理员端）
     */
    List<Classroom> findWithPagination(@Param("offset") Integer offset, 
                                     @Param("size") Integer size,
                                     @Param("schoolId") Long schoolId,
                                     @Param("name") String name,
                                     @Param("floor") Integer floor,
                                     @Param("type") String type,
                                     @Param("status") String status);
    
    /**
     * 分页查询教室列表（校长端）
     */
    List<Classroom> findBySchoolWithPagination(@Param("offset") Integer offset,
                                             @Param("size") Integer size,
                                             @Param("schoolId") Long schoolId,
                                             @Param("name") String name,
                                             @Param("floor") Integer floor,
                                             @Param("type") String type,
                                             @Param("status") String status);
    
    /**
     * 统计教室总数（管理员端）
     */
    Integer countClassrooms(@Param("schoolId") Long schoolId,
                          @Param("name") String name,
                          @Param("floor") Integer floor,
                          @Param("type") String type,
                          @Param("status") String status);
    
    /**
     * 统计教室总数（校长端）
     */
    Integer countBySchool(@Param("schoolId") Long schoolId,
                        @Param("name") String name,
                        @Param("floor") Integer floor,
                        @Param("type") String type,
                        @Param("status") String status);
    
    /**
     * 插入教室
     */
    int insert(Classroom classroom);
    
    /**
     * 更新教室信息
     */
    int update(Classroom classroom);
    
    /**
     * 根据ID删除教室
     */
    int deleteById(@Param("classroomId") Long classroomId);
    
    /**
     * 检查教室名称是否在同一学校内存在
     */
    boolean existsByNameAndSchoolId(@Param("name") String name, @Param("schoolId") Long schoolId);
    
    /**
     * 检查教室名称是否在同一学校内存在（排除指定ID）
     */
    boolean existsByNameAndSchoolIdExcludeId(@Param("name") String name,
                                           @Param("schoolId") Long schoolId,
                                           @Param("classroomId") Long classroomId);

    /**
     * 根据时间段查询空闲教室列表
     */
    List<Classroom> findAvailableClassrooms(@Param("schoolId") Long schoolId,
                                          @Param("courseDate") LocalDate courseDate,
                                          @Param("startTime") LocalTime startTime,
                                          @Param("endTime") LocalTime endTime);
}
