package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.AssignTeachersRequest;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 校长端控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/principal")
@RequiredArgsConstructor
public class PrincipalController {

    private final UserService userService;

    /**
     * 获取未分配校长的老师列表
     */
    @GetMapping("/unassigned-teachers")
    public Result<PageResult<User>> getUnassignedTeachers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String realName) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 校长可以查看所有未分配校长的老师（不限制学校）
            // 这样可以让校长"收养"其他学校的未分配老师
            PageResult<User> result = userService.findUnassignedTeachers(page, size, null, realName);
            log.info("校长查询未分配老师列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询未分配老师列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询未分配老师列表失败");
        }
    }

    /**
     * 获取当前校长名下的老师列表
     */
    @GetMapping("/teachers")
    public Result<PageResult<User>> getMyTeachers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String realName) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long principalId = userPrincipal.getUserId();
            Long schoolId = userPrincipal.getSchoolId();
            
            // 校长只能查看自己名下的老师
            PageResult<User> result = userService.findTeachersWithPrincipal(page, size, schoolId, principalId, realName);
            log.info("校长查询名下老师列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询名下老师列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询名下老师列表失败");
        }
    }

    /**
     * 分配老师给当前校长
     */
    @PostMapping("/assign-teachers")
    public Result<Void> assignTeachers(@Valid @RequestBody AssignTeachersRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long principalId = userPrincipal.getUserId();
            
            // 验证校长角色
            if (!"principal".equals(userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "只有校长可以分配老师");
            }
            
            userService.assignTeachersToPrincipal(principalId, request.getTeacherIds());
            log.info("校长分配老师成功: {} -> {}", principalId, request.getTeacherIds());
            return Result.success("老师分配成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("分配老师失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "分配老师失败");
        }
    }

    /**
     * 解除老师与当前校长的关系
     */
    @DeleteMapping("/teachers/{teacherId}")
    public Result<Void> removeTeacher(@PathVariable Long teacherId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long principalId = userPrincipal.getUserId();

            // 验证校长角色
            if (!"principal".equals(userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "只有校长可以解除老师关系");
            }

            // 验证老师是否存在且属于当前校长
            User teacher = userService.findById(teacherId);
            if (!"teacher".equals(teacher.getRole())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "该用户不是老师角色");
            }

            if (!principalId.equals(teacher.getPrincipalId())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "该老师不属于您管理");
            }

            // 解除关系：将老师的principalId设为null
            userService.removeTeacherFromPrincipal(teacherId);

            log.info("校长解除老师关系成功: 校长ID={}, 老师ID={}", principalId, teacherId);
            return Result.success("老师关系解除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("解除老师关系失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "解除老师关系失败");
        }
    }

    /**
     * 获取所有老师列表（校长端 - 不分页）
     */
    @GetMapping("/teachers/all")
    public Result<List<User>> getAllTeachers(@RequestParam(required = false) String subject) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long principalId = userPrincipal.getUserId();

            // 验证校长角色
            if (!"principal".equals(userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "只有校长可以查看老师列表");
            }

            List<User> teachers = userService.findTeachersByPrincipalIdAndSubject(principalId, subject);
            log.info("校长获取所有老师列表成功，校长ID: {}, 学科: {}, 老师数量: {}", principalId, subject, teachers.size());
            return Result.success("获取老师列表成功", teachers);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取老师列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取老师列表失败");
        }
    }

    /**
     * 获取指定教师的主教科目列表
     */
    @GetMapping("/teachers/{teacherId}/subjects")
    public Result<List<String>> getTeacherSubjects(@PathVariable Long teacherId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long principalId = userPrincipal.getUserId();

            // 验证校长角色
            if (!"principal".equals(userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "只有校长可以查看教师科目");
            }

            // 获取教师信息并验证是否属于当前校长
            User teacher = userService.findById(teacherId);
            if (teacher == null || !"teacher".equals(teacher.getRole())) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND, "教师不存在");
            }

            if (!principalId.equals(teacher.getPrincipalId())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权查看该教师信息");
            }

            // 获取教师的主教科目列表
            List<String> subjects = com.teachingassistant.util.SubjectUtils.stringToArray(teacher.getSubject());
            log.info("校长获取教师科目成功，教师ID: {}, 科目: {}", teacherId, subjects);
            return Result.success("获取教师科目成功", subjects);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教师科目失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取教师科目失败");
        }
    }
}
