package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.CreateClassRequest;
import com.teachingassistant.dto.UpdateClassRequest;
import com.teachingassistant.entity.SchoolClass;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.SchoolClassService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 班级管理控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("")
@RequiredArgsConstructor
public class SchoolClassController {

    private final SchoolClassService schoolClassService;
    
    /**
     * 获取班级列表（管理员端 - 分页）
     */
    @GetMapping("/admin/classes")
    public Result<PageResult<SchoolClass>> getClassList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) String name) {
        try {
            PageResult<SchoolClass> result = schoolClassService.findWithPagination(page, size, schoolId, name);
            return Result.success("获取班级列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取班级列表（校长端 - 分页）
     */
    @GetMapping("/principal/classes")
    public Result<PageResult<SchoolClass>> getPrincipalClassList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            PageResult<SchoolClass> result = schoolClassService.findBySchoolWithPagination(schoolId, page, size, name);
            return Result.success("获取班级列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取所有班级列表（管理员端 - 不分页）
     */
    @GetMapping("/admin/classes/all")
    public Result<List<SchoolClass>> getAllClasses() {
        try {
            List<SchoolClass> classes = schoolClassService.findAll();
            return Result.success("获取班级列表成功", classes);
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取所有班级列表（校长端 - 不分页）
     */
    @GetMapping("/principal/classes/all")
    public Result<List<SchoolClass>> getAllPrincipalClasses(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            List<SchoolClass> classes = schoolClassService.findBySchoolId(schoolId);
            return Result.success("获取班级列表成功", classes);
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 根据ID获取班级详情（管理员端）
     */
    @GetMapping("/admin/classes/{classId}")
    public Result<SchoolClass> getClassById(@PathVariable Long classId) {
        try {
            SchoolClass schoolClass = schoolClassService.findById(classId);
            return Result.success("获取班级详情成功", schoolClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 根据ID获取班级详情（校长端）
     */
    @GetMapping("/principal/classes/{classId}")
    public Result<SchoolClass> getPrincipalClassById(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问该班级");
            }
            
            SchoolClass schoolClass = schoolClassService.findById(classId);
            return Result.success("获取班级详情成功", schoolClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 创建班级（管理员端）
     */
    @PostMapping("/admin/classes")
    public Result<SchoolClass> createClass(@Valid @RequestBody CreateClassRequest request) {
        try {
            if (request.getSchoolId() == null) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
            }
            
            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            
            SchoolClass createdClass = schoolClassService.createClass(schoolClass);
            return Result.success("班级创建成功", createdClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 创建班级（校长端）
     */
    @PostMapping("/principal/classes")
    public Result<SchoolClass> createPrincipalClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Valid @RequestBody CreateClassRequest request) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            schoolClass.setSchoolId(schoolId); // 强制设置为当前用户的学校ID
            
            SchoolClass createdClass = schoolClassService.createClass(schoolClass);
            return Result.success("班级创建成功", createdClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 更新班级信息（管理员端）
     */
    @PutMapping("/admin/classes/{classId}")
    public Result<SchoolClass> updateClass(
            @PathVariable Long classId,
            @Valid @RequestBody UpdateClassRequest request) {
        try {
            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            schoolClass.setClassId(classId);
            
            SchoolClass updatedClass = schoolClassService.updateClass(schoolClass);
            return Result.success("班级更新成功", updatedClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 更新班级信息（校长端）
     */
    @PutMapping("/principal/classes/{classId}")
    public Result<SchoolClass> updatePrincipalClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId,
            @Valid @RequestBody UpdateClassRequest request) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该班级");
            }
            
            SchoolClass existingClass = schoolClassService.findById(classId);
            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            schoolClass.setClassId(classId);
            schoolClass.setSchoolId(existingClass.getSchoolId()); // 保持原有学校ID
            
            SchoolClass updatedClass = schoolClassService.updateClass(schoolClass);
            return Result.success("班级更新成功", updatedClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 删除班级（管理员端）
     */
    @DeleteMapping("/admin/classes/{classId}")
    public Result<Void> deleteClass(@PathVariable Long classId) {
        try {
            schoolClassService.deleteClass(classId);
            return Result.success("班级删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 删除班级（校长端）
     */
    @DeleteMapping("/principal/classes/{classId}")
    public Result<Void> deletePrincipalClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId,
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除该班级");
            }

            schoolClassService.deleteClass(classId);
            return Result.success("班级删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    // ==================== 老师端接口 ====================

    /**
     * 获取班级列表（老师端 - 分页）
     */
    @GetMapping("/teacher/classes")
    public Result<PageResult<SchoolClass>> getTeacherClassList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }

            PageResult<SchoolClass> result = schoolClassService.findBySchoolWithPagination(schoolId, page, size, name);
            return Result.success("获取班级列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 获取所有班级列表（老师端 - 不分页）
     */
    @GetMapping("/teacher/classes/all")
    public Result<List<SchoolClass>> getAllTeacherClasses(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }

            List<SchoolClass> classes = schoolClassService.findBySchoolId(schoolId);
            return Result.success("获取班级列表成功", classes);
        } catch (Exception e) {
            log.error("获取班级列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 根据ID获取班级详情（老师端）
     */
    @GetMapping("/teacher/classes/{classId}")
    public Result<SchoolClass> getTeacherClassById(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId,
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问该班级");
            }

            SchoolClass schoolClass = schoolClassService.findById(classId);
            return Result.success("获取班级详情成功", schoolClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取班级详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 创建班级（老师端）
     * 老师创建的班级会自动归属到该老师所属的校长名下
     */
    @PostMapping("/teacher/classes")
    public Result<SchoolClass> createTeacherClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Valid @RequestBody CreateClassRequest request) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }

            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            schoolClass.setSchoolId(schoolId); // 强制设置为当前用户的学校ID

            SchoolClass createdClass = schoolClassService.createClass(schoolClass);
            return Result.success("班级创建成功", createdClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 更新班级信息（老师端）
     */
    @PutMapping("/teacher/classes/{classId}")
    public Result<SchoolClass> updateTeacherClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId,
            @Valid @RequestBody UpdateClassRequest request) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId,
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该班级");
            }

            SchoolClass existingClass = schoolClassService.findById(classId);
            SchoolClass schoolClass = new SchoolClass();
            BeanUtils.copyProperties(request, schoolClass);
            schoolClass.setClassId(classId);
            schoolClass.setSchoolId(existingClass.getSchoolId()); // 保持原有学校ID

            SchoolClass updatedClass = schoolClassService.updateClass(schoolClass);
            return Result.success("班级更新成功", updatedClass);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    /**
     * 删除班级（老师端）
     */
    @DeleteMapping("/teacher/classes/{classId}")
    public Result<Void> deleteTeacherClass(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classId) {
        try {
            // 验证权限
            if (!schoolClassService.hasPermissionToAccess(classId,
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除该班级");
            }

            schoolClassService.deleteClass(classId);
            return Result.success("班级删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除班级失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
}
