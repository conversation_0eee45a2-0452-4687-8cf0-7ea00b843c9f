package com.teachingassistant.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 课程实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Course extends BaseEntity {
    
    /**
     * 课程ID
     */
    private Long courseId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 授课老师ID
     */
    private Long teacherId;
    
    /**
     * 学生ID
     */
    private Long studentId;
    
    /**
     * 教室ID
     */
    private Long classroomId;
    
    /**
     * 上课日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate courseDate;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;
    
    /**
     * 课程价格
     */
    private BigDecimal price;
    
    /**
     * 年级
     */
    private String gradeLevel;

    /**
     * 科目
     */
    private String subject;

    /**
     * 课程状态：scheduled-已排课，completed-已完成，cancelled-已取消
     */
    private String status;
    
    /**
     * 老师信息（关联查询时使用）
     */
    private User teacher;
    
    /**
     * 学生信息（关联查询时使用）
     */
    private Student student;
    
    /**
     * 教室信息（关联查询时使用）
     */
    private Classroom classroom;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;
}
